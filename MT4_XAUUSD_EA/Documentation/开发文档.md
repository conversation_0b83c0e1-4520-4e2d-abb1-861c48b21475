# MT4 XAUUSD 智能交易系统开发文档

## 技术架构

### 核心组件
1. **参数管理模块**: 处理EA输入参数和配置
2. **图表对象模块**: 管理可拖动的止盈止损线和交易按钮
3. **交易方向模块**: 处理做多/做空模式切换和线条定位
4. **鼠标控制模块**: 实现L/T键选中线条的鼠标控制功能
5. **计算引擎模块**: 实时计算手数、盈亏比、风险参数
6. **事件处理模块**: 处理用户交互事件（按钮、键盘、鼠标）
7. **订单管理模块**: 执行交易订单和状态管理
8. **保本功能模块**: 持仓时的风险管理功能
9. **UI显示模块**: 实时信息面板显示
10. **安全检查模块**: 交易前安全验证

### 关键算法

#### 手数计算公式
```
PipValue = MarketInfo(Symbol(), MODE_TICKVALUE)
RiskPoints = |EntryPrice - StopLossPrice| / Point
RiskMoneyPerLot = RiskPoints * PipValue
Lots = MaxLossUSD / RiskMoneyPerLot
```

#### 盈亏比计算
```
RiskDistance = |EntryPrice - StopLossPrice|
ProfitDistance = |TakeProfitPrice - EntryPrice|
RiskRewardRatio = ProfitDistance / RiskDistance
```

#### 交易方向逻辑
```
做多模式：
- 止损线位置 = CurrentPrice - DefaultSLDistancePoints * Point
- 止盈线位置 = CurrentPrice + (RiskDistance * RiskRewardRatio)

做空模式：
- 止损线位置 = CurrentPrice + DefaultSLDistancePoints * Point
- 止盈线位置 = CurrentPrice - (RiskDistance * RiskRewardRatio)
```

#### 保本功能算法
```
检查条件：
- 必须有持仓
- 当前价格与开仓价有足够距离（>= MinStopLevel）
- 做多：CurrentPrice > OpenPrice + MinStopLevel
- 做空：CurrentPrice < OpenPrice - MinStopLevel

执行保本：
- 修改止损价格到开仓价格
- 更新EA内部状态和图表显示
```

### MT4 API 使用

#### 市场信息获取
- `MarketInfo(Symbol(), MODE_TICKVALUE)`: 获取点值
- `MarketInfo(Symbol(), MODE_TICKSIZE)`: 获取最小变动单位
- `MarketInfo(Symbol(), MODE_LOTSTEP)`: 获取手数步长
- `MarketInfo(Symbol(), MODE_MINLOT)`: 获取最小手数

#### 图表对象操作
- `ObjectCreate()`: 创建图表对象
- `ObjectSet()`: 设置对象属性
- `ObjectGet()`: 获取对象属性
- `ObjectMove()`: 移动对象位置

#### 事件处理
- `OnChartEvent()`: 处理图表事件
- `CHARTEVENT_OBJECT_DRAG`: 对象拖动事件
- `CHARTEVENT_OBJECT_CLICK`: 对象点击事件
- `CHARTEVENT_KEYDOWN`: 键盘按键事件
- `CHARTEVENT_MOUSE_MOVE`: 鼠标移动事件（需启用）

#### 鼠标控制实现
- `ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, true)`: 启用鼠标移动事件
- `ChartXYToTimePrice()`: 鼠标坐标转换为价格
- 状态管理：MouseControlMode, SLMouseControl, TPMouseControl

### 安全机制
1. **交易环境检查**: `IsTradeAllowed()`
2. **保证金检查**: `AccountFreeMarginCheck()`
3. **手数范围验证**: 最小/最大手数限制
4. **止损距离验证**: 最小止损距离检查
5. **重复下单防护**: 魔术数字和订单状态检查

### 性能优化
1. **事件驱动更新**: 仅在必要时更新计算和显示
2. **对象缓存**: 避免重复创建图表对象
3. **计算优化**: 缓存市场信息，减少API调用
4. **内存管理**: 及时清理不需要的对象和变量

## 开发规范

### 代码结构
- 使用模块化设计，功能分离
- 统一的命名规范
- 详细的注释说明
- 错误处理和日志记录

### 测试策略
1. **单元测试**: 各个功能模块独立测试
2. **集成测试**: 模块间交互测试
3. **用户测试**: 实际使用场景测试
4. **压力测试**: 极端条件下的稳定性测试

### 版本控制
- 使用语义化版本号
- 详细的变更日志
- 向后兼容性考虑
