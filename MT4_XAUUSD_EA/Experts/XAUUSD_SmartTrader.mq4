//+------------------------------------------------------------------+
//|                                           XAUUSD_SmartTrader.mq4 |
//|                                    MT4 XAUUSD 智能交易系统 EA    |
//|                                                   Version 1.0.0  |
//+------------------------------------------------------------------+
#property copyright "XAUUSD Smart Trading System"
#property version   "1.00"
#property strict

//--- EA输入参数
input double MaxLossUSD = 50.0;                     // 本次交易最大亏损金额（USD）
input double RiskRewardRatio = 2.0;                 // 盈亏比
input int DefaultSLDistancePoints = 500;            // 默认止损距离（点）
input color LineColorSL = clrRed;                   // 止损线颜色
input color LineColorTP = clrGreen;                 // 止盈线颜色
input double MinLots = 0.01;                        // 最小下单手数
input double MaxLots = 50.0;                        // 最大下单手数
input int Slippage = 3;                             // 允许滑点
input int MagicNumber = 202401;                     // EA订单标识
input bool ShowInfoPanel = true;                    // 显示信息面板
input int InfoPanelX = 20;                          // 信息面板X坐标
input int InfoPanelY = 50;                          // 信息面板Y坐标

//--- 仓位管理参数
input bool UsePercentageRisk = false;               // 使用百分比风险模式（true=百分比，false=固定金额）
input double RiskPercentage = 2.5;                  // 风险百分比（基于总资金的百分比）

//--- 全局变量
string SL_LineName = "XAUUSD_SL_Line";             // 止损线名称
string TP_LineName = "XAUUSD_TP_Line";             // 止盈线名称
string BuyButtonName = "XAUUSD_Buy_Button";        // 买入按钮名称
string SellButtonName = "XAUUSD_Sell_Button";      // 卖出按钮名称
string ResetButtonName = "XAUUSD_Reset_Button";    // 重置按钮名称
string LongButtonName = "XAUUSD_Long_Button";      // 做多模式按钮名称
string ShortButtonName = "XAUUSD_Short_Button";    // 做空模式按钮名称
string BreakevenButtonName = "XAUUSD_Breakeven_Button"; // 保本按钮名称
string RR1to1ButtonName = "XAUUSD_RR_1to1_Button"; // 1:1盈亏比按钮名称
string RR1to2ButtonName = "XAUUSD_RR_1to2_Button"; // 1:2盈亏比按钮名称
// string RiskModeButtonName = "XAUUSD_RiskMode_Button"; // 风险模式切换按钮名称（已移除）

double CurrentLots = 0.0;                           // 当前计算手数
double CurrentSLPrice = 0.0;                        // 当前止损价格
double CurrentTPPrice = 0.0;                        // 当前止盈价格
double CurrentRiskReward = 0.0;                     // 当前盈亏比
double CurrentRiskRewardRatio = 2.0;                // 当前盈亏比设置（可修改）
double TickValue = 0.0;                             // 点值
double TickSize = 0.0;                              // 最小变动单位
double LotStep = 0.0;                               // 手数步长
double MinLot = 0.0;                                // 最小手数
double MaxLot = 0.0;                                // 最大手数

bool IsInitialized = false;                         // 初始化标志
bool HasPosition = false;                           // 持仓标志
int CurrentTicket = -1;                             // 当前订单号

// 鼠标控制模式变量
bool MouseControlMode = false;                    // 鼠标控制模式标志
bool SLMouseControl = false;                      // 止损线鼠标控制
bool TPMouseControl = false;                      // 止盈线鼠标控制
double OriginalSLPrice = 0.0;                     // 原始止损价格（用于取消）
double OriginalTPPrice = 0.0;                     // 原始止盈价格（用于取消）

// 交易方向模式变量
bool IsLongMode = true;                           // 交易方向：true=做多，false=做空

// 风险模式变量
bool CurrentUsePercentageRisk = false;            // 当前使用的风险模式（可修改）

// 🆕 添加缓存的风险金额变量，避免多次计算AccountBalance()导致的不一致
double CachedRiskAmountUSD = 0.0;                 // 缓存的风险金额
double CachedAccountBalance = 0.0;                // 缓存的账户余额

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("XAUUSD Smart Trader EA 初始化开始...");
    
    // 检查交易品种
    if(Symbol() != "XAUUSD")
    {
        Print("警告：此EA专为XAUUSD设计，当前品种为：", Symbol());
        return(INIT_FAILED);
    }
    
    // 获取市场信息
    if(!GetMarketInfo())
    {
        Print("错误：无法获取市场信息");
        return(INIT_FAILED);
    }
    
    // 初始化图表对象
    if(!InitializeChartObjects())
    {
        Print("错误：图表对象初始化失败");
        return(INIT_FAILED);
    }
    
    // 创建信息面板
    if(ShowInfoPanel)
    {
        CreateInfoPanel();
    }
    
    // 初始化线条位置
    InitializeLinePositions();

    // 计算初始参数
    CalculateParameters();
    
    // 初始化当前盈亏比设置
    CurrentRiskRewardRatio = RiskRewardRatio;

    // 初始化风险模式变量
    CurrentUsePercentageRisk = UsePercentageRisk;

    // 初始化模式按钮状态
    UpdateModeButtons();

    // 启用鼠标移动事件监听
    ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, true);

    IsInitialized = true;
    Print("XAUUSD Smart Trader EA 初始化完成");

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("XAUUSD Smart Trader EA 正在卸载，原因：", reason);

    // 禁用鼠标移动事件监听
    ChartSetInteger(0, CHART_EVENT_MOUSE_MOVE, false);

    // 清理图表对象
    CleanupChartObjects();

    Print("XAUUSD Smart Trader EA 卸载完成");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    if(!IsInitialized) return;

    // 使用增强的OnTick处理
    EnhancedOnTick();

    // 检查持仓状态
    CheckPositionStatus();

    // 更新信息面板
    if(ShowInfoPanel)
    {
        UpdateInfoPanel();
    }
}

//+------------------------------------------------------------------+
//| Chart event function                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
    if(!IsInitialized) return;

    // 使用增强的事件处理
    EnhancedOnChartEvent(id, lparam, dparam, sparam);
}

//+------------------------------------------------------------------+
//| 获取市场信息                                                      |
//+------------------------------------------------------------------+
bool GetMarketInfo()
{
    TickValue = MarketInfo(Symbol(), MODE_TICKVALUE);
    TickSize = MarketInfo(Symbol(), MODE_TICKSIZE);
    LotStep = MarketInfo(Symbol(), MODE_LOTSTEP);
    MinLot = MarketInfo(Symbol(), MODE_MINLOT);
    MaxLot = MarketInfo(Symbol(), MODE_MAXLOT);
    
    if(TickValue <= 0 || TickSize <= 0 || LotStep <= 0)
    {
        Print("错误：市场信息无效 - TickValue:", TickValue, " TickSize:", TickSize, " LotStep:", LotStep);
        return false;
    }
    
    Print("市场信息获取成功 - TickValue:", TickValue, " TickSize:", TickSize, " LotStep:", LotStep);
    return true;
}

//+------------------------------------------------------------------+
//| 初始化图表对象                                                    |
//+------------------------------------------------------------------+
bool InitializeChartObjects()
{
    // 创建止损线
    if(!CreateTradingLine(SL_LineName, LineColorSL, "止损线 (可拖动)"))
    {
        Print("错误：无法创建止损线");
        return false;
    }

    // 创建止盈线
    if(!CreateTradingLine(TP_LineName, LineColorTP, "止盈线 (可拖动)"))
    {
        Print("错误：无法创建止盈线");
        return false;
    }

    // 创建交易按钮
    if(!CreateTradingButtons())
    {
        Print("错误：无法创建交易按钮");
        return false;
    }

    Print("图表对象初始化成功");
    return true;
}

//+------------------------------------------------------------------+
//| 创建交易线条                                                      |
//+------------------------------------------------------------------+
bool CreateTradingLine(string lineName, color lineColor, string description)
{
    // 删除已存在的线条
    ObjectDelete(lineName);

    // 创建新线条
    if(!ObjectCreate(lineName, OBJ_HLINE, 0, 0, 0))
    {
        Print("错误：无法创建线条 ", lineName, " 错误代码:", GetLastError());
        return false;
    }

    // 设置线条属性
    ObjectSet(lineName, OBJPROP_COLOR, lineColor);
    ObjectSet(lineName, OBJPROP_WIDTH, 2);
    ObjectSet(lineName, OBJPROP_STYLE, STYLE_SOLID);
    ObjectSet(lineName, OBJPROP_SELECTABLE, true);
    ObjectSet(lineName, OBJPROP_SELECTED, false);
    ObjectSet(lineName, OBJPROP_HIDDEN, false);
    ObjectSet(lineName, OBJPROP_BACK, false);

    // 设置线条描述
    ObjectSetText(lineName, description, 10, "Arial", lineColor);

    return true;
}

//+------------------------------------------------------------------+
//| 创建交易按钮                                                      |
//+------------------------------------------------------------------+
bool CreateTradingButtons()
{
    int buttonWidth = 60;
    int buttonHeight = 28;
    int buttonSpacing = 6;

    // 🆕 移动到左下角
    int chartHeight = (int)ChartGetInteger(0, CHART_HEIGHT_IN_PIXELS);
    int startX = 10;
    int startY = chartHeight - 120; // 距离底部120像素

    // 第一行：交易方向选择按钮
    if(!CreateButton(LongButtonName, "做多", startX, startY, buttonWidth, buttonHeight, clrDarkGreen, clrWhite))
    {
        return false;
    }

    if(!CreateButton(ShortButtonName, "做空", startX + buttonWidth + buttonSpacing, startY, buttonWidth, buttonHeight, clrGray, clrWhite))
    {
        return false;
    }

    // 第二行：交易执行按钮
    int secondRowY = startY + buttonHeight + 6;
    if(!CreateButton(BuyButtonName, "买入", startX, secondRowY, buttonWidth, buttonHeight, clrGreen, clrWhite))
    {
        return false;
    }

    if(!CreateButton(SellButtonName, "卖出", startX + buttonWidth + buttonSpacing, secondRowY, buttonWidth, buttonHeight, clrRed, clrWhite))
    {
        return false;
    }

    if(!CreateButton(ResetButtonName, "重置", startX + (buttonWidth + buttonSpacing) * 2, secondRowY, buttonWidth, buttonHeight, clrBlue, clrWhite))
    {
        return false;
    }

    // 第三行：保本按钮和盈亏比按钮
    int thirdRowY = startY + (buttonHeight + 6) * 2;
    if(!CreateButton(BreakevenButtonName, "保本", startX, thirdRowY, buttonWidth, buttonHeight, clrPurple, clrWhite))
    {
        return false;
    }

    // 1:1盈亏比按钮
    if(!CreateButton(RR1to1ButtonName, "1:1", startX + buttonWidth + buttonSpacing, thirdRowY, buttonWidth, buttonHeight, clrBlue, clrWhite))
    {
        return false;
    }

    // 1:2盈亏比按钮
    if(!CreateButton(RR1to2ButtonName, "1:2", startX + (buttonWidth + buttonSpacing) * 2, thirdRowY, buttonWidth, buttonHeight, clrBlue, clrWhite))
    {
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 创建按钮                                                          |
//+------------------------------------------------------------------+
bool CreateButton(string buttonName, string text, int x, int y, int width, int height, color bgColor, color textColor)
{
    // 删除已存在的按钮
    ObjectDelete(buttonName);

    // 创建按钮
    if(!ObjectCreate(buttonName, OBJ_BUTTON, 0, 0, 0))
    {
        Print("错误：无法创建按钮 ", buttonName, " 错误代码:", GetLastError());
        return false;
    }

    // 设置按钮属性
    ObjectSet(buttonName, OBJPROP_XDISTANCE, x);
    ObjectSet(buttonName, OBJPROP_YDISTANCE, y);
    ObjectSet(buttonName, OBJPROP_XSIZE, width);
    ObjectSet(buttonName, OBJPROP_YSIZE, height);
    ObjectSet(buttonName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSet(buttonName, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
    ObjectSet(buttonName, OBJPROP_BGCOLOR, bgColor);
    ObjectSet(buttonName, OBJPROP_COLOR, textColor);
    ObjectSet(buttonName, OBJPROP_FONTSIZE, 10);
    ObjectSet(buttonName, OBJPROP_SELECTABLE, false);
    ObjectSet(buttonName, OBJPROP_SELECTED, false);
    ObjectSet(buttonName, OBJPROP_HIDDEN, false);
    ObjectSet(buttonName, OBJPROP_ZORDER, 1);

    // 设置按钮文本
    ObjectSetText(buttonName, text, 10, "Arial Bold", textColor);

    return true;
}

//+------------------------------------------------------------------+
//| 更新线条价格标签                                                  |
//+------------------------------------------------------------------+
void UpdateLinePriceLabels()
{
    // 更新止损线标签
    string slText = "止损线: " + DoubleToString(CurrentSLPrice, Digits) + " (可拖动)";
    ObjectSetText(SL_LineName, slText, 10, "Arial", LineColorSL);

    // 更新止盈线标签
    string tpText = "止盈线: " + DoubleToString(CurrentTPPrice, Digits) + " (可拖动)";
    ObjectSetText(TP_LineName, tpText, 10, "Arial", LineColorTP);
}

//+------------------------------------------------------------------+
//| 锁定/解锁线条                                                     |
//+------------------------------------------------------------------+
void SetLinesLocked(bool locked)
{
    ObjectSet(SL_LineName, OBJPROP_SELECTABLE, !locked);
    ObjectSet(TP_LineName, OBJPROP_SELECTABLE, !locked);

    if(locked)
    {
        ObjectSetText(SL_LineName, "止损线: " + DoubleToString(CurrentSLPrice, Digits) + " (已锁定)", 10, "Arial", LineColorSL);
        ObjectSetText(TP_LineName, "止盈线: " + DoubleToString(CurrentTPPrice, Digits) + " (已锁定)", 10, "Arial", LineColorTP);
    }
    else
    {
        UpdateLinePriceLabels();
    }
}

//+------------------------------------------------------------------+
//| 清理图表对象                                                      |
//+------------------------------------------------------------------+
void CleanupChartObjects()
{
    ObjectDelete(SL_LineName);
    ObjectDelete(TP_LineName);
    ObjectDelete(BuyButtonName);
    ObjectDelete(SellButtonName);
    ObjectDelete(ResetButtonName);
    ObjectDelete(LongButtonName);
    ObjectDelete(ShortButtonName);
    ObjectDelete(BreakevenButtonName);
    ObjectDelete(RR1to1ButtonName);
    ObjectDelete(RR1to2ButtonName);

    // 清理信息面板对象
    for(int i = 0; i < 20; i++)
    {
        ObjectDelete("InfoPanel_" + IntegerToString(i));
    }
}
//+------------------------------------------------------------------+
//| 初始化线条位置                                                    |
//+------------------------------------------------------------------+
void InitializeLinePositions()
{
    double currentPrice = (Bid + Ask) / 2.0;

    // 🆕 根据交易方向设置止损位置
    if(IsLongMode)
    {
        // 做多：止损在当前价格下方
        CurrentSLPrice = currentPrice - (DefaultSLDistancePoints * Point);
    }
    else
    {
        // 做空：止损在当前价格上方
        CurrentSLPrice = currentPrice + (DefaultSLDistancePoints * Point);
    }

    ObjectSet(SL_LineName, OBJPROP_PRICE1, CurrentSLPrice);

    // 🆕 根据交易方向和盈亏比计算止盈位置
    double riskDistance = MathAbs(currentPrice - CurrentSLPrice);
    if(IsLongMode)
    {
        // 做多：止盈在当前价格上方
        CurrentTPPrice = currentPrice + (riskDistance * CurrentRiskRewardRatio);
    }
    else
    {
        // 做空：止盈在当前价格下方
        CurrentTPPrice = currentPrice - (riskDistance * CurrentRiskRewardRatio);
    }

    ObjectSet(TP_LineName, OBJPROP_PRICE1, CurrentTPPrice);

    // 更新价格标签
    UpdateLinePriceLabels();

    Print("线条初始化完成 - 交易方向:", (IsLongMode ? "做多" : "做空"), " 当前价格:", currentPrice, " 止损:", CurrentSLPrice, " 止盈:", CurrentTPPrice);
}

//+------------------------------------------------------------------+
//| 计算交易参数                                                      |
//+------------------------------------------------------------------+
void CalculateParameters()
{
    double currentPrice = (Bid + Ask) / 2.0;

    // 计算风险距离（点数）
    double riskDistance = MathAbs(currentPrice - CurrentSLPrice);

    if(riskDistance <= 0)
    {
        CurrentLots = 0.0;
        CurrentRiskReward = 0.0;
        Print("警告：风险距离为零，无法计算手数");
        return;
    }

    // 🆕 使用改进的手数计算方法 - 先获取统一的风险金额
    double riskAmount = GetCurrentRiskAmount();
    CurrentLots = CalculateOptimalLotSize(currentPrice, CurrentSLPrice, riskAmount);

    // 🆕 实时更新止盈线位置（以止损线为主，跟随实时价格）
    if(!TPMouseControl) // 只有在不是手动控制止盈线时才自动更新
    {
        if(IsLongMode)
        {
            // 做多：止盈在当前价格上方
            CurrentTPPrice = currentPrice + (riskDistance * CurrentRiskRewardRatio);
        }
        else
        {
            // 做空：止盈在当前价格下方
            CurrentTPPrice = currentPrice - (riskDistance * CurrentRiskRewardRatio);
        }
        ObjectSet(TP_LineName, OBJPROP_PRICE1, CurrentTPPrice);
    }

    // 计算当前盈亏比
    double profitDistance = MathAbs(CurrentTPPrice - currentPrice);
    CurrentRiskReward = (riskDistance > 0) ? profitDistance / riskDistance : 0.0;
}

//+------------------------------------------------------------------+
//| 🆕 获取当前风险金额（统一计算，避免不一致）                        |
//+------------------------------------------------------------------+
double GetCurrentRiskAmount()
{
    if(CurrentUsePercentageRisk)
    {
        // 缓存账户余额，确保在同一次计算中使用相同的值
        CachedAccountBalance = AccountBalance();
        if(CachedAccountBalance <= 0)
        {
            Print("错误：账户余额无效，无法使用百分比风险模式");
            return MaxLossUSD; // 回退到固定金额模式
        }
        CachedRiskAmountUSD = CachedAccountBalance * (RiskPercentage / 100.0);
        return CachedRiskAmountUSD;
    }
    else
    {
        CachedRiskAmountUSD = MaxLossUSD;
        return MaxLossUSD;
    }
}

//+------------------------------------------------------------------+
//| 计算最优手数                                                      |
//+------------------------------------------------------------------+
double CalculateOptimalLotSize(double entryPrice, double stopLossPrice, double maxRiskUSD)
{
    // 计算风险距离（单位：价格点）
    double riskDistance = MathAbs(entryPrice - stopLossPrice);

    if(riskDistance <= 0)
    {
        Print("错误：风险距离为零，无法计算手数");
        return 0.0;
    }

    // 获取合约规格
    double contractSize = MarketInfo(Symbol(), MODE_LOTSIZE);
    double tickValue = MarketInfo(Symbol(), MODE_TICKVALUE);
    double tickSize = MarketInfo(Symbol(), MODE_TICKSIZE);

    if(contractSize <= 0 || tickValue <= 0 || tickSize <= 0)
    {
        Print("错误：无法获取有效的合约规格");
        return 0.0;
    }

    // 计算每手的风险金额
    double riskPerLot = (riskDistance / tickSize) * tickValue;
    if(riskPerLot <= 0)
    {
        Print("错误：每手风险金额计算无效");
        return 0.0;
    }

    double theoreticalLots = 0.0;

    // 🆕 使用统一的风险金额计算，避免不一致
    double actualRiskAmountUSD = GetCurrentRiskAmount();
    theoreticalLots = actualRiskAmountUSD / riskPerLot;

    // 确保手数在最小和最大手数范围内
    double adjustedLots = NormalizeLotSize(theoreticalLots);

    return adjustedLots;
}

//+------------------------------------------------------------------+
//| 标准化手数                                                        |
//+------------------------------------------------------------------+
double NormalizeLotSize(double lots)
{
    // 获取手数限制
    double minLot = MarketInfo(Symbol(), MODE_MINLOT);
    double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
    double lotStep = MarketInfo(Symbol(), MODE_LOTSTEP);

    // 应用用户设置的限制
    minLot = MathMax(minLot, MinLots);
    maxLot = MathMin(maxLot, MaxLots);

    // 调整到合法范围
    lots = MathMax(lots, minLot);
    lots = MathMin(lots, maxLot);

    // 调整到步长的整数倍
    if(lotStep > 0)
    {
        lots = NormalizeDouble(MathRound(lots / lotStep) * lotStep, 2);
    }

    return lots;
}

//+------------------------------------------------------------------+
//| 计算预期盈亏金额                                                  |
//+------------------------------------------------------------------+
void CalculateExpectedProfitLoss(double &expectedProfit, double &expectedLoss)
{
    if(CurrentLots <= 0)
    {
        expectedProfit = 0.0;
        expectedLoss = 0.0;
        return;
    }

    double currentPrice = (Bid + Ask) / 2.0;
    double tickValue = MarketInfo(Symbol(), MODE_TICKVALUE);
    double tickSize = MarketInfo(Symbol(), MODE_TICKSIZE);

    // 计算预期亏损
    double lossDistance = MathAbs(currentPrice - CurrentSLPrice);
    expectedLoss = (lossDistance / tickSize) * tickValue * CurrentLots;

    // 计算预期盈利
    double profitDistance = MathAbs(CurrentTPPrice - currentPrice);
    expectedProfit = (profitDistance / tickSize) * tickValue * CurrentLots;
}

//+------------------------------------------------------------------+
//| 验证交易参数                                                      |
//+------------------------------------------------------------------+
bool ValidateTradeParameters(double lots, double entryPrice, double stopLoss, double takeProfit)
{
    // 参数合理性检查：手数
    if(lots <= 0)
    {
        Print("错误：手数无效 (", lots, ")");
        return false;
    }

    // 参数合理性检查：手数范围
    double minLot = MarketInfo(Symbol(), MODE_MINLOT);
    double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);

    if(lots < minLot || lots > maxLot)
    {
        Print("错误：手数超出范围，当前:", lots, " 最小:", minLot, " 最大:", maxLot);
        return false;
    }

    // 参数合理性检查：价格有效性
    if(entryPrice <= 0 || stopLoss <= 0 || takeProfit <= 0)
    {
        Print("错误：价格参数无效");
        return false;
    }

    // 参数合理性检查：止损止盈距离
    double minStopLevel = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;
    double slDistance = MathAbs(entryPrice - stopLoss);
    double tpDistance = MathAbs(entryPrice - takeProfit);

    if(slDistance < minStopLevel)
    {
        Print("错误：止损距离过小，当前:", slDistance, " 最小距离:", minStopLevel);
        return false;
    }

    if(tpDistance < minStopLevel)
    {
        Print("错误：止盈距离过小，当前:", tpDistance, " 最小距离:", minStopLevel);
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 处理对象拖动事件                                                  |
//+------------------------------------------------------------------+
void HandleObjectDrag(string objectName)
{
    if(HasPosition)
    {
        // 持仓时不允许拖动线条
        Print("持仓期间不允许调整止盈止损线");
        return;
    }

    if(objectName == SL_LineName)
    {
        // 止损线被拖动
        CurrentSLPrice = ObjectGet(SL_LineName, OBJPROP_PRICE1);

        // 验证止损价格合理性
        double currentPrice = (Bid + Ask) / 2.0;
        double minStopLevel = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;

        if(MathAbs(currentPrice - CurrentSLPrice) < minStopLevel)
        {
            Print("警告：止损距离过小，最小距离:", minStopLevel);
            // 调整到最小距离
            CurrentSLPrice = (CurrentSLPrice < currentPrice) ?
                           currentPrice - minStopLevel : currentPrice + minStopLevel;
            ObjectSet(SL_LineName, OBJPROP_PRICE1, CurrentSLPrice);
        }

        // 根据盈亏比重新计算止盈线位置
        double riskDistance = MathAbs(currentPrice - CurrentSLPrice);
        CurrentTPPrice = currentPrice + (riskDistance * CurrentRiskRewardRatio);
        ObjectSet(TP_LineName, OBJPROP_PRICE1, CurrentTPPrice);

        // 重新计算参数
        CalculateParameters();

        // 更新价格标签
        UpdateLinePriceLabels();

        Print("止损线拖动 - 新止损价:", CurrentSLPrice, " 新止盈价:", CurrentTPPrice);
    }
    else if(objectName == TP_LineName)
    {
        // 止盈线被拖动
        CurrentTPPrice = ObjectGet(TP_LineName, OBJPROP_PRICE1);

        // 验证止盈价格合理性
        double currentPrice = (Bid + Ask) / 2.0;
        double minStopLevel = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;

        if(MathAbs(currentPrice - CurrentTPPrice) < minStopLevel)
        {
            Print("警告：止盈距离过小，最小距离:", minStopLevel);
            // 调整到最小距离
            CurrentTPPrice = (CurrentTPPrice > currentPrice) ?
                           currentPrice + minStopLevel : currentPrice - minStopLevel;
            ObjectSet(TP_LineName, OBJPROP_PRICE1, CurrentTPPrice);
        }

        // 重新计算参数（盈亏比会更新）
        CalculateParameters();

        // 更新价格标签
        UpdateLinePriceLabels();

        Print("止盈线拖动 - 新止盈价:", CurrentTPPrice, " 新盈亏比:", CurrentRiskReward);
    }
}

//+------------------------------------------------------------------+
//| 处理按钮点击事件                                                  |
//+------------------------------------------------------------------+
void HandleButtonClick(string objectName)
{
    // 重置按钮状态
    ObjectSet(objectName, OBJPROP_STATE, false);

    if(objectName == LongButtonName)
    {
        // 切换到做多模式
        SwitchToLongMode();
    }
    else if(objectName == ShortButtonName)
    {
        // 切换到做空模式
        SwitchToShortMode();
    }
    else if(objectName == BuyButtonName)
    {
        if(HasPosition)
        {
            Print("已有持仓，无法开新仓");
            return;
        }

        // 直接执行买入订单，不显示确认对话框
        ExecuteBuyOrder();
    }
    else if(objectName == SellButtonName)
    {
        if(HasPosition)
        {
            Print("已有持仓，无法开新仓");
            return;
        }

        // 直接执行卖出订单，不显示确认对话框
        ExecuteSellOrder();
    }
    else if(objectName == ResetButtonName)
    {
        if(HasPosition)
        {
            Print("持仓期间无法重置线条位置");
            return;
        }

        ResetLinePositions();
        Print("线条位置已重置");
    }
    else if(objectName == BreakevenButtonName)
    {
        // 🆕 保本功能：设置止损到开仓价
        SetBreakeven();
    }
    else if(objectName == RR1to1ButtonName)
    {
        // 设置1:1盈亏比
        SetRiskReward1to1();
    }
    else if(objectName == RR1to2ButtonName)
    {
        // 设置1:2盈亏比
        SetRiskReward1to2();
    }

    // 刷新图表
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| 确认交易执行                                                      |
//+------------------------------------------------------------------+
bool ConfirmTradeExecution(string direction)
{
    // 验证交易参数
    if(!ValidateTradeParameters(CurrentLots, (Bid + Ask) / 2.0, CurrentSLPrice, CurrentTPPrice))
    {
        Print("交易参数验证失败，请检查设置");
        return false;
    }

    // 计算预期盈亏
    double expectedProfit, expectedLoss;
    CalculateExpectedProfitLoss(expectedProfit, expectedLoss);

    // 构建确认信息
    string confirmMsg = StringConcatenate(
        "确认", direction, "交易？\n",
        "品种: ", Symbol(), "\n",
        "手数: ", DoubleToString(CurrentLots, 2), "\n",
        "止损: ", DoubleToString(CurrentSLPrice, Digits), "\n",
        "止盈: ", DoubleToString(CurrentTPPrice, Digits), "\n",
        "盈亏比: ", DoubleToString(CurrentRiskReward, 2), "\n",
        "预期亏损: $", DoubleToString(expectedLoss, 2), "\n",
        "预期盈利: $", DoubleToString(expectedProfit, 2)
    );

    // 显示确认对话框
    int result = MessageBox(confirmMsg, "交易确认", MB_YESNO | MB_ICONQUESTION);

    return (result == IDYES);
}

//+------------------------------------------------------------------+
//| 处理键盘事件                                                      |
//+------------------------------------------------------------------+
void HandleKeyboardEvent(long lparam)
{
    // 处理快捷键
    switch((int)lparam)
    {
        case 66: // B键 - 买入
            if(!HasPosition && !MouseControlMode)
            {
                ObjectSet(BuyButtonName, OBJPROP_STATE, true);
                HandleButtonClick(BuyButtonName);
            }
            break;

        case 83: // S键 - 卖出
            if(!HasPosition && !MouseControlMode)
            {
                ObjectSet(SellButtonName, OBJPROP_STATE, true);
                HandleButtonClick(SellButtonName);
            }
            break;

        case 82: // R键 - 重置
            if(!HasPosition && !MouseControlMode)
            {
                HandleButtonClick(ResetButtonName);
            }
            break;

        case 49: // 1键 - 切换到做多模式
            if(!HasPosition && !MouseControlMode)
            {
                HandleButtonClick(LongButtonName);
            }
            break;

        case 50: // 2键 - 切换到做空模式
            if(!HasPosition && !MouseControlMode)
            {
                HandleButtonClick(ShortButtonName);
            }
            break;

        case 81: // Q键 - 保本
            if(HasPosition && !MouseControlMode)
            {
                HandleButtonClick(BreakevenButtonName);
            }
            break;
            
        case 90: // Z键 - 1:1盈亏比
            if(!MouseControlMode)
            {
                HandleButtonClick(RR1to1ButtonName);
            }
            break;
            
        case 88: // X键 - 1:2盈亏比
            if(!MouseControlMode)
            {
                HandleButtonClick(RR1to2ButtonName);
            }
            break;

        case 80: // P键 - 切换风险模式
            ToggleRiskMode();
            break;
            
        case 76: // L键 - 选中止损线进入鼠标控制模式
            if(!HasPosition)
            {
                ToggleSLMouseControl();
            }
            break;

        case 84: // T键 - 选中止盈线进入鼠标控制模式
            if(!HasPosition)
            {
                ToggleTPMouseControl();
            }
            break;

        case 13: // Enter键 - 确认鼠标控制模式的修改
            if(MouseControlMode)
            {
                ConfirmMouseControl();
            }
            break;

        case 27: // ESC键 - 取消选择或退出鼠标控制模式
            if(MouseControlMode)
            {
                CancelMouseControl();
            }
            else
            {
                ObjectSetInteger(0, SL_LineName, OBJPROP_SELECTED, false);
                ObjectSetInteger(0, TP_LineName, OBJPROP_SELECTED, false);
                ChartRedraw();
            }
            break;
    }
}

//+------------------------------------------------------------------+
//| 处理鼠标事件                                                      |
//+------------------------------------------------------------------+
void HandleMouseEvent(long lparam, double dparam)
{
    // 如果不在鼠标控制模式，直接返回
    if(!MouseControlMode)
        return;

    // 获取鼠标位置对应的价格
    int x = (int)lparam;
    int y = (int)dparam;

    datetime time;
    double price;
    int window;

    if(ChartXYToTimePrice(0, x, y, window, time, price))
    {
        // 根据控制模式更新相应的线条
        if(SLMouseControl)
        {
            UpdateSLPriceByMouse(price);
        }
        else if(TPMouseControl)
        {
            UpdateTPPriceByMouse(price);
        }
    }
}

//+------------------------------------------------------------------+
//| 更新按钮状态                                                      |
//+------------------------------------------------------------------+
void UpdateButtonStates()
{
    if(HasPosition)
    {
        // 持仓时禁用交易按钮
        ObjectSet(BuyButtonName, OBJPROP_COLOR, clrGray);
        ObjectSet(SellButtonName, OBJPROP_COLOR, clrGray);
        ObjectSetText(BuyButtonName, "持仓中", 10, "Arial Bold", clrGray);
        ObjectSetText(SellButtonName, "持仓中", 10, "Arial Bold", clrGray);

        // 🆕 持仓时启用保本按钮
        ObjectSet(BreakevenButtonName, OBJPROP_COLOR, clrWhite);
        ObjectSet(BreakevenButtonName, OBJPROP_BGCOLOR, clrPurple);
        ObjectSetText(BreakevenButtonName, "保本", 10, "Arial Bold", clrWhite);
        
        // 🆕 持仓时启用盈亏比按钮
        ObjectSet(RR1to1ButtonName, OBJPROP_COLOR, clrWhite);
        ObjectSet(RR1to1ButtonName, OBJPROP_BGCOLOR, clrBlue);
        ObjectSetText(RR1to1ButtonName, "1:1", 10, "Arial Bold", clrWhite);
        
        ObjectSet(RR1to2ButtonName, OBJPROP_COLOR, clrWhite);
        ObjectSet(RR1to2ButtonName, OBJPROP_BGCOLOR, clrBlue);
        ObjectSetText(RR1to2ButtonName, "1:2", 10, "Arial Bold", clrWhite);
    }
    else
    {
        // 无持仓时启用交易按钮
        ObjectSet(BuyButtonName, OBJPROP_COLOR, clrWhite);
        ObjectSet(SellButtonName, OBJPROP_COLOR, clrWhite);
        ObjectSetText(BuyButtonName, "买入", 10, "Arial Bold", clrWhite);
        ObjectSetText(SellButtonName, "卖出", 10, "Arial Bold", clrWhite);

        // 🆕 无持仓时禁用保本按钮
        ObjectSet(BreakevenButtonName, OBJPROP_COLOR, clrGray);
        ObjectSet(BreakevenButtonName, OBJPROP_BGCOLOR, clrDarkGray);
        ObjectSetText(BreakevenButtonName, "无持仓", 10, "Arial", clrGray);
        
        // 🆕 无持仓时启用盈亏比按钮（用于设置默认止盈线）
        ObjectSet(RR1to1ButtonName, OBJPROP_COLOR, clrWhite);
        ObjectSet(RR1to1ButtonName, OBJPROP_BGCOLOR, clrBlue);
        ObjectSetText(RR1to1ButtonName, "1:1", 10, "Arial Bold", clrWhite);
        
        ObjectSet(RR1to2ButtonName, OBJPROP_COLOR, clrWhite);
        ObjectSet(RR1to2ButtonName, OBJPROP_BGCOLOR, clrBlue);
        ObjectSetText(RR1to2ButtonName, "1:2", 10, "Arial Bold", clrWhite);
    }
}

//+------------------------------------------------------------------+
//| 检查持仓状态                                                      |
//+------------------------------------------------------------------+
void CheckPositionStatus()
{
    bool foundPosition = false;

    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
            {
                foundPosition = true;
                CurrentTicket = OrderTicket();
                break;
            }
        }
    }

    HasPosition = foundPosition;
}

//+------------------------------------------------------------------+
//| 创建信息面板                                                      |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
    int panelX = InfoPanelX;
    int panelY = InfoPanelY + 50; // 调整位置（按钮移到左下角后）
    int lineHeight = 20; // 增加行高
    int labelWidth = 200;

    // 创建面板背景
    CreateInfoLabel("InfoPanel_Background", "", panelX - 5, panelY - 5, 250, 280, clrBlack, clrWhite, 1);

    // 创建标题
    CreateInfoLabel("InfoPanel_Title", "XAUUSD 智能交易信息", panelX, panelY, labelWidth, lineHeight, clrNONE, clrYellow, 12);
    panelY += lineHeight + 8; // 标题后多空一行

    // 创建分隔线
    CreateInfoLabel("InfoPanel_Separator1", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", panelX, panelY, labelWidth, lineHeight, clrNONE, clrGray, 8);
    panelY += lineHeight + 5;

    // 🆕 添加交易方向显示
    CreateInfoLabel("InfoPanel_TradeMode", "交易方向: 做多", panelX, panelY, labelWidth, lineHeight, clrNONE, clrLime, 10);
    panelY += lineHeight + 5;

    // 🆕 添加仓位管理模式显示
    CreateInfoLabel("InfoPanel_RiskMode", "风险模式: 固定金额", panelX, panelY, labelWidth, lineHeight, clrNONE, clrAqua, 10);
    panelY += lineHeight + 3;

    // 🆕 添加风险百分比显示
    CreateInfoLabel("InfoPanel_RiskPercentage", "风险百分比: 0.00%", panelX, panelY, labelWidth, lineHeight, clrNONE, clrWhite, 10);
    panelY += lineHeight + 3;

    // 创建信息标签 - 每行之间空一行
    CreateInfoLabel("InfoPanel_MaxLoss", "最大亏损金额: $0.00", panelX, panelY, labelWidth, lineHeight, clrNONE, clrWhite, 10);
    panelY += lineHeight + 3;

    CreateInfoLabel("InfoPanel_CurrentLots", "当前手数: 0.00", panelX, panelY, labelWidth, lineHeight, clrNONE, clrWhite, 10);
    panelY += lineHeight + 3;

    CreateInfoLabel("InfoPanel_RiskReward", "盈亏比: 0.00", panelX, panelY, labelWidth, lineHeight, clrNONE, clrWhite, 10);
    panelY += lineHeight + 5; // 盈亏比后多空一点

    CreateInfoLabel("InfoPanel_SLPrice", "止损价格: 0.00000", panelX, panelY, labelWidth, lineHeight, clrNONE, clrRed, 10);
    panelY += lineHeight + 3;

    CreateInfoLabel("InfoPanel_TPPrice", "止盈价格: 0.00000", panelX, panelY, labelWidth, lineHeight, clrNONE, clrGreen, 10);
    panelY += lineHeight + 5;

    CreateInfoLabel("InfoPanel_Separator2", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", panelX, panelY, labelWidth, lineHeight, clrNONE, clrGray, 8);
    panelY += lineHeight + 3;

    CreateInfoLabel("InfoPanel_ExpectedLoss", "预期亏损: $0.00", panelX, panelY, labelWidth, lineHeight, clrNONE, clrOrange, 10);
    panelY += lineHeight + 3;

    CreateInfoLabel("InfoPanel_ExpectedProfit", "预期盈利: $0.00", panelX, panelY, labelWidth, lineHeight, clrNONE, clrLime, 10);
    panelY += lineHeight + 3;

    CreateInfoLabel("InfoPanel_Status", "状态: 准备就绪", panelX, panelY, labelWidth, lineHeight, clrNONE, clrAqua, 10);
    panelY += lineHeight + 5;

    CreateInfoLabel("InfoPanel_Separator3", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━", panelX, panelY, labelWidth, lineHeight, clrNONE, clrGray, 8);
    panelY += lineHeight + 3;

    CreateInfoLabel("InfoPanel_Hotkeys", "快捷键: 1=做多 2=做空 B=买入 S=卖出 R=重置 Q=保本", panelX, panelY, labelWidth, lineHeight, clrNONE, clrSilver, 8);
    panelY += lineHeight + 3;
    
    CreateInfoLabel("InfoPanel_Hotkeys2", "快捷键: Z=1:1盈亏比 X=1:2盈亏比", panelX, panelY, labelWidth, lineHeight, clrNONE, clrSilver, 8);
    panelY += lineHeight + 3;
    
    CreateInfoLabel("InfoPanel_Hotkeys3", "线条控制: L=止损线 T=止盈线 Enter=确认 ESC=取消", panelX, panelY, labelWidth, lineHeight, clrNONE, clrSilver, 8);
    panelY += lineHeight + 3;
    
    CreateInfoLabel("InfoPanel_Hotkeys4", "其他: P=切换风险模式", panelX, panelY, labelWidth, lineHeight, clrNONE, clrSilver, 8);

    Print("信息面板创建完成");
}

//+------------------------------------------------------------------+
//| 创建信息标签                                                      |
//+------------------------------------------------------------------+
void CreateInfoLabel(string name, string text, int x, int y, int width, int height, color bgColor, color textColor, int fontSize)
{
    // 删除已存在的标签
    ObjectDelete(name);

    // 创建标签
    if(ObjectCreate(name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSet(name, OBJPROP_XDISTANCE, x);
        ObjectSet(name, OBJPROP_YDISTANCE, y);
        ObjectSet(name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSet(name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
        ObjectSet(name, OBJPROP_COLOR, textColor);
        ObjectSet(name, OBJPROP_FONTSIZE, fontSize);
        ObjectSet(name, OBJPROP_SELECTABLE, false);
        ObjectSet(name, OBJPROP_HIDDEN, false);
        ObjectSet(name, OBJPROP_ZORDER, 2);

        if(bgColor != clrNONE)
        {
            ObjectSet(name, OBJPROP_BGCOLOR, bgColor);
        }

        ObjectSetText(name, text, fontSize, "Consolas", textColor);
    }
}

//+------------------------------------------------------------------+
//| 更新信息面板                                                      |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
    if(!ShowInfoPanel) return;

    // 计算预期盈亏
    double expectedProfit, expectedLoss;
    CalculateExpectedProfitLoss(expectedProfit, expectedLoss);

    // 🆕 更新最大亏损金额 - 使用统一的风险金额计算
    double currentRiskAmount = GetCurrentRiskAmount();
    string maxLossText;
    if(CurrentUsePercentageRisk)
    {
        maxLossText = "最大亏损金额: $" + DoubleToString(currentRiskAmount, 2) + " (" + DoubleToString(RiskPercentage, 2) + "%)";
    }
    else
    {
        maxLossText = "最大亏损金额: $" + DoubleToString(currentRiskAmount, 2);
    }
    ObjectSetText("InfoPanel_MaxLoss", maxLossText, 10, "Consolas", clrWhite);

    // 🆕 更新风险百分比显示
    ObjectSetText("InfoPanel_RiskPercentage", "风险百分比: " + DoubleToString(RiskPercentage, 2) + "%", 10, "Consolas", clrWhite);

    // 更新当前手数
    ObjectSetText("InfoPanel_CurrentLots", "当前手数: " + DoubleToString(CurrentLots, 2), 10, "Consolas", clrWhite);

    // 🆕 更新交易方向显示
    string modeText = "交易方向: " + (IsLongMode ? "做多" : "做空");
    color modeColor = IsLongMode ? clrLime : clrOrange;
    ObjectSetText("InfoPanel_TradeMode", modeText, 10, "Consolas", modeColor);

    // 🆕 更新风险模式显示
    string riskModeText = "风险模式: " + (CurrentUsePercentageRisk ? "百分比" : "固定金额");
    color riskModeColor = CurrentUsePercentageRisk ? clrAqua : clrWhite;
    ObjectSetText("InfoPanel_RiskMode", riskModeText, 10, "Consolas", riskModeColor);

    // 更新盈亏比
    color rrColor = (CurrentRiskReward >= CurrentRiskRewardRatio) ? clrLime : clrOrange;
    ObjectSetText("InfoPanel_RiskReward", "盈亏比: " + DoubleToString(CurrentRiskReward, 2), 10, "Consolas", rrColor);

    // 更新止损价格
    ObjectSetText("InfoPanel_SLPrice", "止损价格: " + DoubleToString(CurrentSLPrice, Digits), 10, "Consolas", clrRed);

    // 更新止盈价格
    ObjectSetText("InfoPanel_TPPrice", "止盈价格: " + DoubleToString(CurrentTPPrice, Digits), 10, "Consolas", clrGreen);

    // 更新预期亏损
    ObjectSetText("InfoPanel_ExpectedLoss", "预期亏损: $" + DoubleToString(expectedLoss, 2), 10, "Consolas", clrOrange);

    // 更新预期盈利
    ObjectSetText("InfoPanel_ExpectedProfit", "预期盈利: $" + DoubleToString(expectedProfit, 2), 10, "Consolas", clrLime);

    // 更新状态
    string status;
    color statusColor;

    if(HasPosition)
    {
        status = "持仓中";
        statusColor = clrYellow;
    }
    else if(MouseControlMode)
    {
        if(SLMouseControl)
        {
            status = "止损线鼠标控制";
            statusColor = clrOrange;
        }
        else if(TPMouseControl)
        {
            status = "止盈线鼠标控制";
            statusColor = clrOrange;
        }
        else
        {
            status = "鼠标控制模式";
            statusColor = clrOrange;
        }
    }
    else
    {
        status = "准备就绪";
        statusColor = clrAqua;
    }

    ObjectSetText("InfoPanel_Status", "状态: " + status, 10, "Consolas", statusColor);

    // 如果有持仓，显示当前盈亏
    if(HasPosition)
    {
        double currentPL = GetCurrentProfitLoss();
        color plColor = (currentPL >= 0) ? clrLime : clrRed;
        string plText = "当前盈亏: $" + DoubleToString(currentPL, 2);

        // 创建或更新当前盈亏标签
        if(ObjectFind("InfoPanel_CurrentPL") < 0)
        {
            CreateInfoLabel("InfoPanel_CurrentPL", plText, InfoPanelX, InfoPanelY + 220, 200, 18, clrNONE, plColor, 10);
        }
        else
        {
            ObjectSetText("InfoPanel_CurrentPL", plText, 10, "Consolas", plColor);
        }
    }
    else
    {
        // 删除当前盈亏标签
        ObjectDelete("InfoPanel_CurrentPL");
    }
}

//+------------------------------------------------------------------+
//| 执行买入订单                                                      |
//+------------------------------------------------------------------+
void ExecuteBuyOrder()
{
    Print("开始执行买入订单...");

    // 执行最终安全检查
    if(!FinalSafetyCheck())
    {
        // 安全检查失败时，在图表上显示错误信息
        MessageBox("买入订单安全检查失败，请检查交易环境和参数设置", "下单失败", MB_OK | MB_ICONERROR);
        return;
    }

    // 获取最新价格
    double entryPrice = Ask;
    RefreshRates(); // 刷新价格

    // 执行买入订单
    int ticket = OrderSend(
        Symbol(),           // 交易品种
        OP_BUY,            // 订单类型
        CurrentLots,       // 手数
        entryPrice,        // 开仓价格
        Slippage,          // 滑点
        CurrentSLPrice,    // 止损价格
        CurrentTPPrice,    // 止盈价格
        "XAUUSD Smart Trader - BUY", // 订单注释
        MagicNumber,       // 魔术数字
        0,                 // 过期时间
        clrGreen           // 箭头颜色
    );

    if(ticket > 0)
    {
        // 验证订单
        if(OrderSelect(ticket, SELECT_BY_TICKET))
        {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
            {
                CurrentTicket = ticket;
                HasPosition = true;  // 确保下单后更新状态

                // 锁定线条
                SetLinesLocked(true);

                // 更新按钮状态
                UpdateButtonStates();

                Print("买入订单执行成功，订单号:", ticket);
                Print("买入订单执行成功！订单号: ", ticket, " 手数: ", CurrentLots);

                // 刷新图表
                ChartRedraw();
            }
            else
            {
                Print("买入订单验证失败：订单不匹配");
                MessageBox("买入订单验证失败：订单不匹配", "下单失败", MB_OK | MB_ICONERROR);
            }
        }
        else
        {
            Print("买入订单验证失败：无法选择订单");
            MessageBox("买入订单验证失败：无法选择订单", "下单失败", MB_OK | MB_ICONERROR);
        }
    }
    else
    {
        int error = GetLastError();
        string errorMsg = "买入订单执行失败，错误代码: " + IntegerToString(error) + " 错误描述: " + ErrorDescription(error);
        Print(errorMsg);
        MessageBox(errorMsg, "下单失败", MB_OK | MB_ICONERROR);
    }
}

//+------------------------------------------------------------------+
//| 执行卖出订单                                                      |
//+------------------------------------------------------------------+
void ExecuteSellOrder()
{
    Print("开始执行卖出订单...");

    // 执行最终安全检查
    if(!FinalSafetyCheck())
    {
        // 安全检查失败时，在图表上显示错误信息
        MessageBox("卖出订单安全检查失败，请检查交易环境和参数设置", "下单失败", MB_OK | MB_ICONERROR);
        return;
    }

    // 获取最新价格
    double entryPrice = Bid;
    RefreshRates(); // 刷新价格

    // 执行卖出订单
    int ticket = OrderSend(
        Symbol(),           // 交易品种
        OP_SELL,           // 订单类型
        CurrentLots,       // 手数
        entryPrice,        // 开仓价格
        Slippage,          // 滑点
        CurrentSLPrice,    // 止损价格
        CurrentTPPrice,    // 止盈价格
        "XAUUSD Smart Trader - SELL", // 订单注释
        MagicNumber,       // 魔术数字
        0,                 // 过期时间
        clrRed             // 箭头颜色
    );

    if(ticket > 0)
    {
        // 验证订单
        if(OrderSelect(ticket, SELECT_BY_TICKET))
        {
            if(OrderSymbol() == Symbol() && OrderMagicNumber() == MagicNumber)
            {
                CurrentTicket = ticket;
                HasPosition = true;  // 确保下单后更新状态

                // 锁定线条
                SetLinesLocked(true);

                // 更新按钮状态
                UpdateButtonStates();

                Print("卖出订单执行成功，订单号:", ticket);
                Print("卖出订单执行成功！订单号: ", ticket, " 手数: ", CurrentLots);

                // 刷新图表
                ChartRedraw();
            }
            else
            {
                Print("卖出订单验证失败：订单不匹配");
                MessageBox("卖出订单验证失败：订单不匹配", "下单失败", MB_OK | MB_ICONERROR);
            }
        }
        else
        {
            Print("卖出订单验证失败：无法选择订单");
            MessageBox("卖出订单验证失败：无法选择订单", "下单失败", MB_OK | MB_ICONERROR);
        }
    }
    else
    {
        int error = GetLastError();
        string errorMsg = "卖出订单执行失败，错误代码: " + IntegerToString(error) + " 错误描述: " + ErrorDescription(error);
        Print(errorMsg);
        MessageBox(errorMsg, "下单失败", MB_OK | MB_ICONERROR);
    }
}

//+------------------------------------------------------------------+
//| 获取当前盈亏                                                      |
//+------------------------------------------------------------------+
double GetCurrentProfitLoss()
{
    if(!HasPosition || CurrentTicket <= 0)
        return 0.0;

    if(OrderSelect(CurrentTicket, SELECT_BY_TICKET))
    {
        return OrderProfit() + OrderSwap() + OrderCommission();
    }

    return 0.0;
}

//+------------------------------------------------------------------+
//| 关闭当前持仓                                                      |
//+------------------------------------------------------------------+
bool CloseCurrentPosition()
{
    if(!HasPosition || CurrentTicket <= 0)
        return false;

    if(!OrderSelect(CurrentTicket, SELECT_BY_TICKET))
        return false;

    double closePrice = (OrderType() == OP_BUY) ? Bid : Ask;

    bool result = OrderClose(
        CurrentTicket,
        OrderLots(),
        closePrice,
        Slippage,
        clrYellow
    );

    if(result)
    {
        Print("持仓关闭成功，订单号:", CurrentTicket);

        // 重置状态
        HasPosition = false;
        CurrentTicket = -1;

        // 解锁线条
        SetLinesLocked(false);

        // 更新按钮状态
        UpdateButtonStates();

        Print("持仓已关闭");
        return true;
    }
    else
    {
        int error = GetLastError();
        Print("关闭持仓失败，错误代码:", error, " 错误描述:", ErrorDescription(error));
        return false;
    }
}

//+------------------------------------------------------------------+
//| 错误描述                                                          |
//+------------------------------------------------------------------+
string ErrorDescription(int errorCode)
{
    switch(errorCode)
    {
        case ERR_NO_ERROR: return "无错误";
        case ERR_NO_RESULT: return "无结果";
        case ERR_COMMON_ERROR: return "一般错误";
        case ERR_INVALID_TRADE_PARAMETERS: return "无效的交易参数";
        case ERR_SERVER_BUSY: return "服务器忙";
        case ERR_OLD_VERSION: return "版本过旧";
        case ERR_NO_CONNECTION: return "无连接";
        case ERR_NOT_ENOUGH_RIGHTS: return "权限不足";
        case ERR_TOO_FREQUENT_REQUESTS: return "请求过于频繁";
        case ERR_MALFUNCTIONAL_TRADE: return "交易功能故障";
        case ERR_ACCOUNT_DISABLED: return "账户被禁用";
        case ERR_INVALID_ACCOUNT: return "无效账户";
        case ERR_TRADE_TIMEOUT: return "交易超时";
        case ERR_INVALID_PRICE: return "无效价格";
        case ERR_INVALID_STOPS: return "无效止损";
        case ERR_INVALID_TRADE_VOLUME: return "无效交易量";
        case ERR_MARKET_CLOSED: return "市场关闭";
        case ERR_TRADE_DISABLED: return "交易被禁用";
        case ERR_NOT_ENOUGH_MONEY: return "资金不足";
        case ERR_PRICE_CHANGED: return "价格改变";
        case ERR_OFF_QUOTES: return "无报价";
        case ERR_BROKER_BUSY: return "经纪商忙";
        case ERR_REQUOTE: return "重新报价";
        case ERR_ORDER_LOCKED: return "订单锁定";
        case ERR_LONG_POSITIONS_ONLY_ALLOWED: return "只允许多头持仓";
        case ERR_TOO_MANY_REQUESTS: return "请求过多";
        default: return "未知错误 (" + IntegerToString(errorCode) + ")";
    }
}

//+------------------------------------------------------------------+
//| 重置线条位置                                                      |
//+------------------------------------------------------------------+
void ResetLinePositions()
{
    InitializeLinePositions();
    CalculateParameters();
    Print("线条位置已重置");
}









//+------------------------------------------------------------------+
//| 风险控制验证                                                      |
//+------------------------------------------------------------------+
bool RiskControlValidation(double lots, double entryPrice, double stopLoss, double takeProfit)
{
    // 基本参数验证
    if(!ValidateTradeParameters(lots, entryPrice, stopLoss, takeProfit))
    {
        return false;
    }

    // 风险控制：最大风险限制检查
    double riskDistance = MathAbs(entryPrice - stopLoss);
    double tickValue = MarketInfo(Symbol(), MODE_TICKVALUE);
    double tickSize = MarketInfo(Symbol(), MODE_TICKSIZE);

    if(tickValue > 0 && tickSize > 0)
    {
        double actualRiskUSD = (riskDistance / tickSize) * tickValue * lots;
        // 🆕 使用缓存的风险金额，确保与计算手数时使用的是同一个值
        double maxAllowedRisk = CachedRiskAmountUSD;

        if(actualRiskUSD > maxAllowedRisk)
        {
            Print("错误：风险超出限制，实际风险: $", DoubleToString(actualRiskUSD, 2),
                  " 最大允许: $", DoubleToString(maxAllowedRisk, 2),
                  " 账户余额: $", DoubleToString(CachedAccountBalance, 2),
                  " 风险百分比: ", DoubleToString(RiskPercentage, 2), "%");
            return false;
        }
    }
    else
    {
        Print("错误：无法获取有效的点值或最小变动单位");
        return false;
    }

    // 风险控制：盈亏比检查（考虑交易方向）
    double profitDistance;
    if(IsLongMode)
    {
        // 做多：止盈应该高于入场价
        profitDistance = takeProfit - entryPrice;
    }
    else
    {
        // 做空：止盈应该低于入场价
        profitDistance = entryPrice - takeProfit;
    }

    double currentRR = (riskDistance > 0) ? profitDistance / riskDistance : 0.0;

    if(currentRR < 0.5) // 最低盈亏比要求
    {
        Print("错误：盈亏比过低 (", DoubleToString(currentRR, 2), ")，最低要求 0.5");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 交易前最终安全检查                                                |
//+------------------------------------------------------------------+
bool FinalSafetyCheck()
{
    // 检查是否已有持仓
    if(HasPosition)
    {
        Print("错误：已有持仓，无法开新仓");
        return false;
    }

    // 风险控制验证（包含参数合理性和风险控制）
    double entryPrice = (Bid + Ask) / 2.0;
    if(!RiskControlValidation(CurrentLots, entryPrice, CurrentSLPrice, CurrentTPPrice))
    {
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 性能优化和监控                                                    |
//+------------------------------------------------------------------+
void PerformanceOptimization()
{
    // 🆕 实时更新频率优化 - 为了让止盈线实时跟随价格变动，提高更新频率
    static datetime lastCalculation = 0;
    if(TimeCurrent() - lastCalculation < 0.5) // 每0.5秒最多计算一次（提高频率）
    {
        return;
    }
    lastCalculation = TimeCurrent();

    // 只在价格变化时重新计算
    static double lastBid = 0, lastAsk = 0;
    if(MathAbs(Bid - lastBid) < Point && MathAbs(Ask - lastAsk) < Point)
    {
        return;
    }
    lastBid = Bid;
    lastAsk = Ask;

    // 执行必要的计算 - 这会实时更新止盈线位置
    CalculateParameters();

    // 🆕 实时更新价格标签
    UpdateLinePriceLabels();
}

//+------------------------------------------------------------------+
//| 内存管理和清理                                                    |
//+------------------------------------------------------------------+
void MemoryManagement()
{
    // 定期清理不需要的对象
    static datetime lastCleanup = 0;
    if(TimeCurrent() - lastCleanup < 300) // 每5分钟清理一次
    {
        return;
    }
    lastCleanup = TimeCurrent();

    // 清理无效的图表对象
    int totalObjects = ObjectsTotal();
    for(int i = totalObjects - 1; i >= 0; i--)
    {
        string objName = ObjectName(i);
        if(StringFind(objName, "XAUUSD_") == 0)
        {
            // 检查对象是否仍然有效
            if(ObjectFind(objName) < 0)
            {
                ObjectDelete(objName);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 错误恢复机制                                                      |
//+------------------------------------------------------------------+
void ErrorRecovery()
{
    // 检查关键对象是否存在
    if(ObjectFind(SL_LineName) < 0)
    {
        Print("止损线丢失，重新创建...");
        CreateTradingLine(SL_LineName, LineColorSL, "止损线 (可拖动)");
        ObjectSet(SL_LineName, OBJPROP_PRICE1, CurrentSLPrice);
    }

    if(ObjectFind(TP_LineName) < 0)
    {
        Print("止盈线丢失，重新创建...");
        CreateTradingLine(TP_LineName, LineColorTP, "止盈线 (可拖动)");
        ObjectSet(TP_LineName, OBJPROP_PRICE1, CurrentTPPrice);
    }

    // 检查按钮是否存在
    if(ObjectFind(BuyButtonName) < 0 || ObjectFind(SellButtonName) < 0 || ObjectFind(ResetButtonName) < 0)
    {
        Print("交易按钮丢失，重新创建...");
        CreateTradingButtons();
    }

    // 检查信息面板
    if(ShowInfoPanel && ObjectFind("InfoPanel_Title") < 0)
    {
        Print("信息面板丢失，重新创建...");
        CreateInfoPanel();
    }
}

//+------------------------------------------------------------------+
//| 数据验证和修复                                                    |
//+------------------------------------------------------------------+
void DataValidationAndRepair()
{
    // 验证和修复关键变量
    if(CurrentLots < 0 || CurrentLots > MaxLots)
    {
        Print("手数数据异常，重新计算...");
        CalculateParameters();
    }

    if(CurrentSLPrice <= 0 || CurrentTPPrice <= 0)
    {
        Print("价格数据异常，重新初始化...");
        InitializeLinePositions();
    }

    // 验证市场信息
    if(TickValue <= 0 || TickSize <= 0)
    {
        Print("市场信息异常，重新获取...");
        GetMarketInfo();
    }
}

//+------------------------------------------------------------------+
//| 增强的OnTick函数                                                  |
//+------------------------------------------------------------------+
void EnhancedOnTick()
{
    // 性能优化
    PerformanceOptimization();

    // 错误恢复
    ErrorRecovery();

    // 数据验证
    DataValidationAndRepair();

    // 内存管理
    MemoryManagement();
}

//+------------------------------------------------------------------+
//| 增强的OnChartEvent函数                                            |
//+------------------------------------------------------------------+
void EnhancedOnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    // 处理键盘事件
    if(id == CHARTEVENT_KEYDOWN)
    {
        HandleKeyboardEvent(lparam);
        return;
    }

    // 处理鼠标事件
    if(id == CHARTEVENT_MOUSE_MOVE)
    {
        HandleMouseEvent(lparam, dparam);
        return;
    }

    // 处理对象拖动事件
    if(id == CHARTEVENT_OBJECT_DRAG)
    {
        HandleObjectDrag(sparam);
        return;
    }

    // 处理按钮点击事件
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        HandleButtonClick(sparam);
        return;
    }

    // 处理图表变化事件
    if(id == CHARTEVENT_CHART_CHANGE)
    {
        Print("图表发生变化，重新调整对象位置");
        ChartRedraw();
        return;
    }
}

//+------------------------------------------------------------------+
//| 调试和日志功能                                                    |
//+------------------------------------------------------------------+
void DebugLog(string message, int level = 0)
{
    string prefix = "";
    switch(level)
    {
        case 0: prefix = "[INFO] "; break;
        case 1: prefix = "[WARN] "; break;
        case 2: prefix = "[ERROR] "; break;
        case 3: prefix = "[DEBUG] "; break;
    }

    Print(prefix + "XAUUSD EA: " + message);
}

//+------------------------------------------------------------------+
//| 获取EA状态信息                                                    |
//+------------------------------------------------------------------+
string GetEAStatusInfo()
{
    string status = StringConcatenate(
        "EA状态信息:\n",
        "版本: 1.0.0\n",
        "初始化状态: ", (IsInitialized ? "已初始化" : "未初始化"), "\n",
        "持仓状态: ", (HasPosition ? "有持仓" : "无持仓"), "\n",
        "当前手数: ", DoubleToString(CurrentLots, 2), "\n",
        "止损价格: ", DoubleToString(CurrentSLPrice, Digits), "\n",
        "止盈价格: ", DoubleToString(CurrentTPPrice, Digits), "\n",
        "盈亏比: ", DoubleToString(CurrentRiskReward, 2), "\n",
        "最大亏损: $", DoubleToString(MaxLossUSD, 2), "\n",
        "账户余额: $", DoubleToString(AccountBalance(), 2), "\n",
        "可用保证金: $", DoubleToString(AccountFreeMargin(), 2)
    );

    return status;
}

//+------------------------------------------------------------------+
//| 切换止损线鼠标控制模式                                            |
//+------------------------------------------------------------------+
void ToggleSLMouseControl()
{
    if(SLMouseControl)
    {
        // 如果已经在止损线控制模式，退出
        ExitMouseControlMode();
    }
    else
    {
        // 进入止损线鼠标控制模式
        EnterSLMouseControlMode();
    }
}

//+------------------------------------------------------------------+
//| 切换止盈线鼠标控制模式                                            |
//+------------------------------------------------------------------+
void ToggleTPMouseControl()
{
    if(TPMouseControl)
    {
        // 如果已经在止盈线控制模式，退出
        ExitMouseControlMode();
    }
    else
    {
        // 进入止盈线鼠标控制模式
        EnterTPMouseControlMode();
    }
}

//+------------------------------------------------------------------+
//| 进入止损线鼠标控制模式                                            |
//+------------------------------------------------------------------+
void EnterSLMouseControlMode()
{
    // 保存原始价格
    OriginalSLPrice = CurrentSLPrice;

    // 设置控制模式标志
    MouseControlMode = true;
    SLMouseControl = true;
    TPMouseControl = false;

    // 高亮止损线
    ObjectSet(SL_LineName, OBJPROP_WIDTH, 3);
    ObjectSet(SL_LineName, OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetText(SL_LineName, "止损线 (鼠标控制中 - Enter确认, ESC取消)", 12, "Arial Bold", LineColorSL);

    // 显示提示信息
    Print("止损线鼠标控制模式已激活 - 移动鼠标调整价格，Enter确认，ESC取消");

    ChartRedraw();
}

//+------------------------------------------------------------------+
//| 进入止盈线鼠标控制模式                                            |
//+------------------------------------------------------------------+
void EnterTPMouseControlMode()
{
    // 保存原始价格
    OriginalTPPrice = CurrentTPPrice;

    // 设置控制模式标志
    MouseControlMode = true;
    TPMouseControl = true;
    SLMouseControl = false;

    // 高亮止盈线
    ObjectSet(TP_LineName, OBJPROP_WIDTH, 3);
    ObjectSet(TP_LineName, OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetText(TP_LineName, "止盈线 (鼠标控制中 - Enter确认, ESC取消)", 12, "Arial Bold", LineColorTP);

    // 显示提示信息
    Print("止盈线鼠标控制模式已激活 - 移动鼠标调整价格，Enter确认，ESC取消");

    ChartRedraw();
}

//+------------------------------------------------------------------+
//| 退出鼠标控制模式                                                  |
//+------------------------------------------------------------------+
void ExitMouseControlMode()
{
    // 重置控制模式标志
    MouseControlMode = false;
    SLMouseControl = false;
    TPMouseControl = false;

    // 恢复线条样式
    ObjectSet(SL_LineName, OBJPROP_WIDTH, 2);
    ObjectSet(TP_LineName, OBJPROP_WIDTH, 2);

    // 更新价格标签
    UpdateLinePriceLabels();

    ChartRedraw();
    Print("退出鼠标控制模式");
}

//+------------------------------------------------------------------+
//| 通过鼠标更新止损价格                                              |
//+------------------------------------------------------------------+
void UpdateSLPriceByMouse(double mousePrice)
{
    // 验证价格合理性
    double currentPrice = (Bid + Ask) / 2.0;
    double minStopLevel = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;

    // 确保止损距离不小于最小止损距离
    if(MathAbs(currentPrice - mousePrice) < minStopLevel)
    {
        // 调整到最小距离
        mousePrice = (mousePrice < currentPrice) ?
                    currentPrice - minStopLevel : currentPrice + minStopLevel;
    }

    // 更新止损价格
    CurrentSLPrice = mousePrice;
    ObjectSet(SL_LineName, OBJPROP_PRICE1, CurrentSLPrice);

    // 🆕 根据盈亏比和实时价格重新计算止盈线位置
    double riskDistance = MathAbs(currentPrice - CurrentSLPrice);
    if(IsLongMode)
    {
        // 做多：止盈在当前价格上方
        CurrentTPPrice = currentPrice + (riskDistance * CurrentRiskRewardRatio);
    }
    else
    {
        // 做空：止盈在当前价格下方
        CurrentTPPrice = currentPrice - (riskDistance * CurrentRiskRewardRatio);
    }
    ObjectSet(TP_LineName, OBJPROP_PRICE1, CurrentTPPrice);

    // 重新计算参数（但不会再次更新止盈线，因为TPMouseControl为false）
    CalculateParameters();

    // 更新显示
    string slText = "止损线: " + DoubleToString(CurrentSLPrice, Digits) + " (鼠标控制中)";
    ObjectSetText(SL_LineName, slText, 12, "Arial Bold", LineColorSL);

    // 🆕 同时更新止盈线显示
    string tpText = "止盈线: " + DoubleToString(CurrentTPPrice, Digits) + " (自动跟随)";
    ObjectSetText(TP_LineName, tpText, 10, "Arial", LineColorTP);
}

//+------------------------------------------------------------------+
//| 通过鼠标更新止盈价格                                              |
//+------------------------------------------------------------------+
void UpdateTPPriceByMouse(double mousePrice)
{
    // 验证价格合理性
    double currentPrice = (Bid + Ask) / 2.0;
    double minStopLevel = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;

    // 确保止盈距离不小于最小止损距离
    if(MathAbs(currentPrice - mousePrice) < minStopLevel)
    {
        // 调整到最小距离
        mousePrice = (mousePrice > currentPrice) ?
                    currentPrice + minStopLevel : currentPrice - minStopLevel;
    }

    // 更新止盈价格
    CurrentTPPrice = mousePrice;
    ObjectSet(TP_LineName, OBJPROP_PRICE1, CurrentTPPrice);

    // 重新计算参数（盈亏比会更新）
    CalculateParameters();

    // 更新显示
    string tpText = "止盈线: " + DoubleToString(CurrentTPPrice, Digits) + " (鼠标控制中)";
    ObjectSetText(TP_LineName, tpText, 12, "Arial Bold", LineColorTP);
}

//+------------------------------------------------------------------+
//| 确认鼠标控制模式的修改                                            |
//+------------------------------------------------------------------+
void ConfirmMouseControl()
{
    if(SLMouseControl)
    {
        Print("止损价格已确认: " + DoubleToString(CurrentSLPrice, Digits));
    }
    else if(TPMouseControl)
    {
        Print("止盈价格已确认: " + DoubleToString(CurrentTPPrice, Digits));
    }

    // 退出控制模式
    ExitMouseControlMode();
}

//+------------------------------------------------------------------+
//| 取消鼠标控制模式的修改                                            |
//+------------------------------------------------------------------+
void CancelMouseControl()
{
    if(SLMouseControl)
    {
        // 恢复原始止损价格
        CurrentSLPrice = OriginalSLPrice;
        ObjectSet(SL_LineName, OBJPROP_PRICE1, CurrentSLPrice);

        // 重新计算止盈价格
        double currentPrice = (Bid + Ask) / 2.0;
        double riskDistance = MathAbs(currentPrice - CurrentSLPrice);
        CurrentTPPrice = currentPrice + (riskDistance * CurrentRiskRewardRatio);
        ObjectSet(TP_LineName, OBJPROP_PRICE1, CurrentTPPrice);

        Print("止损线调整已取消 - 恢复价格:", CurrentSLPrice);
    }
    else if(TPMouseControl)
    {
        // 恢复原始止盈价格
        CurrentTPPrice = OriginalTPPrice;
        ObjectSet(TP_LineName, OBJPROP_PRICE1, CurrentTPPrice);

        Print("止盈线调整已取消 - 恢复价格:", CurrentTPPrice);
    }

    // 重新计算参数
    CalculateParameters();

    // 退出控制模式
    ExitMouseControlMode();
}

//+------------------------------------------------------------------+
//| 切换到做多模式                                                    |
//+------------------------------------------------------------------+
void SwitchToLongMode()
{
    if(HasPosition)
    {
        Print("持仓期间无法切换交易方向");
        return;
    }

    if(IsLongMode)
    {
        Print("已经是做多模式");
        return;
    }

    IsLongMode = true;

    // 更新按钮状态
    UpdateModeButtons();

    // 重新初始化线条位置
    InitializeLinePositions();

    // 重新计算参数
    CalculateParameters();

    Print("已切换到做多模式");
}

//+------------------------------------------------------------------+
//| 切换到做空模式                                                    |
//+------------------------------------------------------------------+
void SwitchToShortMode()
{
    if(HasPosition)
    {
        Print("持仓期间无法切换交易方向");
        return;
    }

    if(!IsLongMode)
    {
        Print("已经是做空模式");
        return;
    }

    IsLongMode = false;

    // 更新按钮状态
    UpdateModeButtons();

    // 重新初始化线条位置
    InitializeLinePositions();

    // 重新计算参数
    CalculateParameters();

    Print("已切换到做空模式");
}

//+------------------------------------------------------------------+
//| 更新模式按钮状态                                                  |
//+------------------------------------------------------------------+
void UpdateModeButtons()
{
    if(IsLongMode)
    {
        // 做多模式：做多按钮高亮，做空按钮灰色
        ObjectSet(LongButtonName, OBJPROP_BGCOLOR, clrDarkGreen);
        ObjectSet(LongButtonName, OBJPROP_COLOR, clrWhite);
        ObjectSetText(LongButtonName, "做多", 10, "Arial Bold", clrWhite);

        ObjectSet(ShortButtonName, OBJPROP_BGCOLOR, clrGray);
        ObjectSet(ShortButtonName, OBJPROP_COLOR, clrDarkGray);
        ObjectSetText(ShortButtonName, "做空", 10, "Arial", clrDarkGray);
    }
    else
    {
        // 做空模式：做空按钮高亮，做多按钮灰色
        ObjectSet(ShortButtonName, OBJPROP_BGCOLOR, clrDarkRed);
        ObjectSet(ShortButtonName, OBJPROP_COLOR, clrWhite);
        ObjectSetText(ShortButtonName, "做空", 10, "Arial Bold", clrWhite);

        ObjectSet(LongButtonName, OBJPROP_BGCOLOR, clrGray);
        ObjectSet(LongButtonName, OBJPROP_COLOR, clrDarkGray);
        ObjectSetText(LongButtonName, "做多", 10, "Arial", clrDarkGray);
    }
}

//+------------------------------------------------------------------+
//| 保本功能：设置止损到开仓价                                        |
//+------------------------------------------------------------------+
void SetBreakeven()
{
    if(!HasPosition)
    {
        Print("无持仓，无法设置保本");
        return;
    }

    if(CurrentTicket <= 0)
    {
        Print("无效的订单号，无法设置保本");
        return;
    }

    // 选择订单
    if(!OrderSelect(CurrentTicket, SELECT_BY_TICKET))
    {
        Print("无法选择订单，订单号:", CurrentTicket);
        return;
    }

    double openPrice = OrderOpenPrice();
    double currentStopLoss = OrderStopLoss();

    // 检查是否已经是保本状态
    if(MathAbs(currentStopLoss - openPrice) < Point * 2)
    {
        Print("止损已经设置为保本价格");
        return;
    }

    // 检查保本是否有利可图
    double currentPrice = (OrderType() == OP_BUY) ? Bid : Ask;
    double minStopLevel = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;
    bool canBreakeven = false;

    if(OrderType() == OP_BUY)
    {
        // 做多：当前价格必须高于开仓价至少一个最小止损距离
        canBreakeven = (currentPrice > openPrice + minStopLevel);
    }
    else if(OrderType() == OP_SELL)
    {
        // 做空：当前价格必须低于开仓价至少一个最小止损距离
        canBreakeven = (currentPrice < openPrice - minStopLevel);
    }

    if(!canBreakeven)
    {
        Print("当前价格不适合设置保本，需要有足够的盈利空间");
        return;
    }

    // 修改止损到开仓价
    bool result = OrderModify(CurrentTicket, OrderOpenPrice(), openPrice, OrderTakeProfit(), 0, clrYellow);

    if(result)
    {
        // 更新EA内部的止损价格
        CurrentSLPrice = openPrice;
        ObjectSet(SL_LineName, OBJPROP_PRICE1, CurrentSLPrice);

        // 更新价格标签
        UpdateLinePriceLabels();

        Print("保本设置成功，止损已调整到开仓价:", openPrice);

        // 刷新图表
        ChartRedraw();
    }
    else
    {
        int error = GetLastError();
        Print("保本设置失败，错误代码:", error, " 错误描述:", ErrorDescription(error));
    }
}

//+------------------------------------------------------------------+
//| 设置1:1盈亏比                                                    |
//+------------------------------------------------------------------+
void SetRiskReward1to1()
{
    // 更新全局盈亏比变量
    CurrentRiskRewardRatio = 1.0;
    
    if(HasPosition && CurrentTicket > 0)
    {
        // 有持仓：修改订单的止盈价格
        if(!OrderSelect(CurrentTicket, SELECT_BY_TICKET))
        {
            Print("无法选择订单，订单号:", CurrentTicket);
            return;
        }
        
        double openPrice = OrderOpenPrice();
        double currentStopLoss = OrderStopLoss();
        
        // 根据交易方向和1:1盈亏比计算新的止盈价格
        double newTakeProfit = 0.0;
        if(OrderType() == OP_BUY)
        {
            double riskDistance = openPrice - currentStopLoss;
            newTakeProfit = openPrice + riskDistance; // 1:1盈亏比
        }
        else if(OrderType() == OP_SELL)
        {
            double riskDistance = currentStopLoss - openPrice;
            newTakeProfit = openPrice - riskDistance; // 1:1盈亏比
        }
        
        // 修改订单的止盈价格
        bool result = OrderModify(CurrentTicket, OrderOpenPrice(), currentStopLoss, newTakeProfit, 0, clrBlue);
        
        if(result)
        {
            // 更新EA内部的止盈价格
            CurrentTPPrice = newTakeProfit;
            ObjectSet(TP_LineName, OBJPROP_PRICE1, CurrentTPPrice);
            
            // 更新价格标签
            UpdateLinePriceLabels();
            
            Print("1:1盈亏比设置成功，新的止盈价:", newTakeProfit);
        }
        else
        {
            int error = GetLastError();
            Print("1:1盈亏比设置失败，错误代码:", error, " 错误描述:", ErrorDescription(error));
        }
    }
    else
    {
        // 无持仓：更新当前止盈价格和图表线条
        double currentPrice = (Bid + Ask) / 2.0;
        double riskDistance = MathAbs(currentPrice - CurrentSLPrice);
        
        // 根据交易方向和1:1盈亏比计算新的止盈价格
        if(IsLongMode)
        {
            // 做多：止盈在当前价格上方
            CurrentTPPrice = currentPrice + riskDistance; // 1:1盈亏比
        }
        else
        {
            // 做空：止盈在当前价格下方
            CurrentTPPrice = currentPrice - riskDistance; // 1:1盈亏比
        }
        
        // 更新图表线条
        ObjectSet(TP_LineName, OBJPROP_PRICE1, CurrentTPPrice);
        
        // 更新价格标签
        UpdateLinePriceLabels();
        
        // 更新当前盈亏比
        CurrentRiskReward = 1.0;
        
        Print("1:1盈亏比设置成功，新的止盈价:", CurrentTPPrice);
    }
}

//+------------------------------------------------------------------+
//| 设置1:2盈亏比                                                    |
//+------------------------------------------------------------------+
void SetRiskReward1to2()
{
    // 更新全局盈亏比变量
    CurrentRiskRewardRatio = 2.0;
    
    if(HasPosition && CurrentTicket > 0)
    {
        // 有持仓：修改订单的止盈价格
        if(!OrderSelect(CurrentTicket, SELECT_BY_TICKET))
        {
            Print("无法选择订单，订单号:", CurrentTicket);
            return;
        }
        
        double openPrice = OrderOpenPrice();
        double currentStopLoss = OrderStopLoss();
        
        // 根据交易方向和1:2盈亏比计算新的止盈价格
        double newTakeProfit = 0.0;
        if(OrderType() == OP_BUY)
        {
            double riskDistance = openPrice - currentStopLoss;
            newTakeProfit = openPrice + (riskDistance * 2.0); // 1:2盈亏比
        }
        else if(OrderType() == OP_SELL)
        {
            double riskDistance = currentStopLoss - openPrice;
            newTakeProfit = openPrice - (riskDistance * 2.0); // 1:2盈亏比
        }
        
        // 修改订单的止盈价格
        bool result = OrderModify(CurrentTicket, OrderOpenPrice(), currentStopLoss, newTakeProfit, 0, clrBlue);
        
        if(result)
        {
            // 更新EA内部的止盈价格
            CurrentTPPrice = newTakeProfit;
            ObjectSet(TP_LineName, OBJPROP_PRICE1, CurrentTPPrice);
            
            // 更新价格标签
            UpdateLinePriceLabels();
            
            Print("1:2盈亏比设置成功，新的止盈价:", newTakeProfit);
        }
        else
        {
            int error = GetLastError();
            Print("1:2盈亏比设置失败，错误代码:", error, " 错误描述:", ErrorDescription(error));
        }
    }
    else
    {
        // 无持仓：更新当前止盈价格和图表线条
        double currentPrice = (Bid + Ask) / 2.0;
        double riskDistance = MathAbs(currentPrice - CurrentSLPrice);
        
        // 根据交易方向和1:2盈亏比计算新的止盈价格
        if(IsLongMode)
        {
            // 做多：止盈在当前价格上方
            CurrentTPPrice = currentPrice + (riskDistance * 2.0); // 1:2盈亏比
        }
        else
        {
            // 做空：止盈在当前价格下方
            CurrentTPPrice = currentPrice - (riskDistance * 2.0); // 1:2盈亏比
        }
        
        // 更新图表线条
        ObjectSet(TP_LineName, OBJPROP_PRICE1, CurrentTPPrice);
        
        // 更新价格标签
        UpdateLinePriceLabels();
        
        // 更新当前盈亏比
        CurrentRiskReward = 2.0;
        
        Print("1:2盈亏比设置成功，新的止盈价:", CurrentTPPrice);
    }
}


//+------------------------------------------------------------------+
//| 切换风险模式                                                    |
//+------------------------------------------------------------------+
void ToggleRiskMode()
{
    // 切换风险模式
    CurrentUsePercentageRisk = !CurrentUsePercentageRisk;
    
    // 更新面板显示
    UpdateInfoPanel();
    
    // 重新计算参数（因为风险模式改变了）
    CalculateParameters();
    
    Print("风险模式已切换到: ", (CurrentUsePercentageRisk ? "百分比风险" : "固定风险"));
}
    
//+------------------------------------------------------------------+
//| 更新风险模式按钮状态                                            |
//+------------------------------------------------------------------+
// void UpdateRiskModeButton() - 已移除
// {
//     string buttonText = CurrentUsePercentageRisk ? "百分比风险" : "固定风险";
//     color buttonColor = CurrentUsePercentageRisk ? clrOrange : clrTeal;
//
//     ObjectSetText(RiskModeButtonName, buttonText, 10, "Arial Bold", clrWhite);
//     ObjectSet(RiskModeButtonName, OBJPROP_BGCOLOR, buttonColor);
// }
